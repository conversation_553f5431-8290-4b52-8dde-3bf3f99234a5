using Application.Abstractions.AI;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SharedKernel;
using System.Net.Http.Json;
using System.Text.Json;

namespace Infrastructure.AI;

internal sealed class AIResumeCustomizationService : IAIResumeCustomizationService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<AIResumeCustomizationService> _logger;
    private readonly AIServiceOptions _options;

    public AIResumeCustomizationService(
        HttpClient httpClient,
        ILogger<AIResumeCustomizationService> logger,
        IOptions<AIServiceOptions> options)
    {
        _httpClient = httpClient;
        _logger = logger;
        _options = options.Value;
    }

    public async Task<Result<AICustomizationResponse>> CustomizeResumeAsync(
        AICustomizationRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting AI resume customization for job: {JobTitle}", request.JobTitle);

            var prompt = CreateCustomizationPrompt(request);
            var aiRequest = new DeepSeekRequest
            {
                Model = _options.Model,
                Messages = new[]
                {
                    new DeepSeekMessage
                    {
                        Role = "system",
                        Content = "You are an expert resume writer. Your task is to customize resumes to match specific job requirements while maintaining authenticity and accuracy."
                    },
                    new DeepSeekMessage
                    {
                        Role = "user",
                        Content = prompt
                    }
                },
                MaxTokens = _options.MaxTokens,
                Temperature = _options.Temperature
            };

            var response = await _httpClient.PostAsJsonAsync(
                _options.Endpoint,
                aiRequest,
                cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("AI service returned error status: {StatusCode}", response.StatusCode);
                return Result.Failure<AICustomizationResponse>(AIServiceErrors.ServiceUnavailable());
            }

            var aiResponse = await response.Content.ReadFromJsonAsync<DeepSeekResponse>(cancellationToken);

            if (aiResponse?.Choices?.FirstOrDefault()?.Message?.Content is null)
            {
                _logger.LogError("AI service returned invalid response");
                return Result.Failure<AICustomizationResponse>(AIServiceErrors.InvalidResponse());
            }

            var customizedContent = aiResponse.Choices.First().Message.Content;
            
            // Parse the AI response to extract customized content and summary
            var parsedResponse = ParseAIResponse(customizedContent);

            _logger.LogInformation("AI resume customization completed successfully");

            return Result.Success(parsedResponse);
        }
        catch (TaskCanceledException)
        {
            _logger.LogWarning("AI service request timed out");
            return Result.Failure<AICustomizationResponse>(AIServiceErrors.Timeout());
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error occurred while calling AI service");
            return Result.Failure<AICustomizationResponse>(AIServiceErrors.ServiceUnavailable());
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Failed to parse AI service response");
            return Result.Failure<AICustomizationResponse>(AIServiceErrors.InvalidResponse());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error occurred during AI processing");
            return Result.Failure<AICustomizationResponse>(AIServiceErrors.ProcessingFailed(ex.Message));
        }
    }

    private static string CreateCustomizationPrompt(AICustomizationRequest request)
    {
        return $@"
Please customize the following resume to better match the job requirements. 

**Job Details:**
- Position: {request.JobTitle}
- Company: {request.CompanyUrl}
- Job Description: {request.JobDescription}

**Original Resume Content:**
{request.OriginalResumeContent}

**Instructions:**
1. Customize the resume to highlight relevant skills and experiences for this specific job
2. Maintain all factual information - do not add false experiences or skills
3. Reorganize and emphasize content to better match job requirements
4. Use keywords from the job description where appropriate and truthful
5. Keep the same HTML structure and formatting

**Response Format:**
Please respond with a JSON object containing:
- ""customizedContent"": The customized resume HTML content
- ""summary"": A brief summary of changes made
- ""confidence"": A confidence score (0.0-1.0) for the customization quality

Example:
{{
  ""customizedContent"": ""<div>customized resume HTML here</div>"",
  ""summary"": ""Emphasized relevant skills and reorganized experience section"",
  ""confidence"": 0.85
}}";
    }

    private static AICustomizationResponse ParseAIResponse(string aiResponse)
    {
        try
        {
            AIResponseJson? jsonResponse = JsonSerializer.Deserialize<AIResponseJson>(aiResponse);
            
            return new AICustomizationResponse(
                jsonResponse?.CustomizedContent ?? aiResponse,
                jsonResponse?.Summary ?? "AI customization completed",
                jsonResponse?.Confidence ?? 0.7);
        }
        catch
        {
            // Fallback if JSON parsing fails
            return new AICustomizationResponse(
                aiResponse,
                "AI customization completed (fallback parsing)",
                0.6);
        }
    }

    private sealed class AIResponseJson
    {
        public string? CustomizedContent { get; init; }
        public string? Summary { get; init; }
        public double Confidence { get; init; }
    }
}
