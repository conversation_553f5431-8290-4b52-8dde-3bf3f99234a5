﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)xunit.core\2.9.3\build\xunit.core.targets" Condition="Exists('$(NuGetPackageRoot)xunit.core\2.9.3\build\xunit.core.targets')" />
    <Import Project="$(NuGetPackageRoot)system.text.json\9.0.0\buildTransitive\net8.0\System.Text.Json.targets" Condition="Exists('$(NuGetPackageRoot)system.text.json\9.0.0\buildTransitive\net8.0\System.Text.Json.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.options\9.0.6\buildTransitive\net8.0\Microsoft.Extensions.Options.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.options\9.0.6\buildTransitive\net8.0\Microsoft.Extensions.Options.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\9.0.6\buildTransitive\net8.0\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\9.0.6\buildTransitive\net8.0\Microsoft.Extensions.Logging.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.binder\9.0.6\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.binder\9.0.6\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.telemetry.abstractions\9.6.0\buildTransitive\net8.0\Microsoft.Extensions.Telemetry.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.telemetry.abstractions\9.6.0\buildTransitive\net8.0\Microsoft.Extensions.Telemetry.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.http.resilience\9.6.0\buildTransitive\net8.0\Microsoft.Extensions.Http.Resilience.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.http.resilience\9.6.0\buildTransitive\net8.0\Microsoft.Extensions.Http.Resilience.targets')" />
    <Import Project="$(NuGetPackageRoot)emptyfiles\4.4.0\buildTransitive\EmptyFiles.targets" Condition="Exists('$(NuGetPackageRoot)emptyfiles\4.4.0\buildTransitive\EmptyFiles.targets')" />
    <Import Project="$(NuGetPackageRoot)shouldly\4.3.0\buildTransitive\Shouldly.targets" Condition="Exists('$(NuGetPackageRoot)shouldly\4.3.0\buildTransitive\Shouldly.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.testplatform.testhost\17.14.1\build\net8.0\Microsoft.TestPlatform.TestHost.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.testplatform.testhost\17.14.1\build\net8.0\Microsoft.TestPlatform.TestHost.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.codecoverage\17.14.1\build\netstandard2.0\Microsoft.CodeCoverage.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.codecoverage\17.14.1\build\netstandard2.0\Microsoft.CodeCoverage.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.net.test.sdk\17.14.1\build\net8.0\Microsoft.NET.Test.Sdk.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.net.test.sdk\17.14.1\build\net8.0\Microsoft.NET.Test.Sdk.targets')" />
    <Import Project="$(NuGetPackageRoot)coverlet.collector\6.0.4\build\netstandard2.0\coverlet.collector.targets" Condition="Exists('$(NuGetPackageRoot)coverlet.collector\6.0.4\build\netstandard2.0\coverlet.collector.targets')" />
  </ImportGroup>
</Project>